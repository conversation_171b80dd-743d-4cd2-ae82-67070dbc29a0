<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BAKASANA Responsiveness Tester</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }
        
        .device-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .device-btn:hover {
            background: #f0f0f0;
        }
        
        .device-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .iframe-container {
            position: relative;
            background: #333;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 600px;
        }
        
        .device-frame {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .device-frame iframe {
            border: none;
            display: block;
        }
        
        .device-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .test-results {
            padding: 20px;
            background: #f8f9fa;
        }
        
        .test-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-checkbox {
            margin-right: 10px;
        }
        
        .test-status {
            margin-left: auto;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-pass {
            background: #d4edda;
            color: #155724;
        }
        
        .status-fail {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .tools {
            padding: 20px;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .tool-btn {
            padding: 8px 16px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .tool-btn:hover {
            background: #218838;
        }
        
        .results-panel {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 BAKASANA Responsiveness Tester</h1>
            <p>Interactive testing tool for responsive design validation</p>
        </div>
        
        <div class="controls">
            <label><strong>Select Device:</strong></label>
            <button class="device-btn active" data-width="1920" data-height="1080" data-name="Desktop FHD">Desktop FHD</button>
            <button class="device-btn" data-width="1366" data-height="768" data-name="Laptop">Laptop</button>
            <button class="device-btn" data-width="1024" data-height="768" data-name="iPad Landscape">iPad Landscape</button>
            <button class="device-btn" data-width="768" data-height="1024" data-name="iPad Portrait">iPad Portrait</button>
            <button class="device-btn" data-width="430" data-height="932" data-name="iPhone 14 Pro Max">iPhone 14 Pro Max</button>
            <button class="device-btn" data-width="390" data-height="844" data-name="iPhone 14">iPhone 14</button>
            <button class="device-btn" data-width="375" data-height="667" data-name="iPhone SE">iPhone SE</button>
            <button class="device-btn" data-width="360" data-height="800" data-name="Galaxy S21">Galaxy S21</button>
            <button class="device-btn" data-width="320" data-height="568" data-name="Small Mobile">Small Mobile</button>
        </div>
        
        <div class="iframe-container">
            <div class="device-info" id="deviceInfo">Desktop FHD - 1920x1080</div>
            <div class="device-frame" id="deviceFrame">
                <iframe id="testFrame" src="http://localhost:3002" width="1920" height="1080"></iframe>
            </div>
        </div>
        
        <div class="tools">
            <button class="tool-btn" onclick="runResponsivenessTests()">🔍 Run Tests</button>
            <button class="tool-btn" onclick="checkTouchTargets()">👆 Check Touch Targets</button>
            <button class="tool-btn" onclick="checkHorizontalScroll()">↔️ Check Horizontal Scroll</button>
            <button class="tool-btn" onclick="checkViewportInfo()">📏 Viewport Info</button>
            <button class="tool-btn" onclick="takeScreenshot()">📸 Screenshot</button>
            <button class="tool-btn" onclick="clearResults()">🗑️ Clear Results</button>
        </div>
        
        <div class="test-results">
            <h3>📋 Test Checklist</h3>
            <div class="test-item">
                <input type="checkbox" class="test-checkbox" id="test-navigation">
                <label for="test-navigation">Navigation displays correctly</label>
                <span class="test-status status-pending" id="status-navigation">Pending</span>
            </div>
            <div class="test-item">
                <input type="checkbox" class="test-checkbox" id="test-layout">
                <label for="test-layout">Layout adapts properly</label>
                <span class="test-status status-pending" id="status-layout">Pending</span>
            </div>
            <div class="test-item">
                <input type="checkbox" class="test-checkbox" id="test-text">
                <label for="test-text">Text is readable</label>
                <span class="test-status status-pending" id="status-text">Pending</span>
            </div>
            <div class="test-item">
                <input type="checkbox" class="test-checkbox" id="test-images">
                <label for="test-images">Images scale properly</label>
                <span class="test-status status-pending" id="status-images">Pending</span>
            </div>
            <div class="test-item">
                <input type="checkbox" class="test-checkbox" id="test-touch">
                <label for="test-touch">Touch targets are adequate</label>
                <span class="test-status status-pending" id="status-touch">Pending</span>
            </div>
            <div class="test-item">
                <input type="checkbox" class="test-checkbox" id="test-scroll">
                <label for="test-scroll">No horizontal scrolling</label>
                <span class="test-status status-pending" id="status-scroll">Pending</span>
            </div>
        </div>
        
        <div class="results-panel" id="resultsPanel">
            Ready to test! Select a device size and click "Run Tests" to begin automated testing.
        </div>
    </div>

    <script>
        let currentDevice = { width: 1920, height: 1080, name: 'Desktop FHD' };
        
        // Device switching
        document.querySelectorAll('.device-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                document.querySelectorAll('.device-btn').forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');
                
                // Get device dimensions
                const width = parseInt(this.dataset.width);
                const height = parseInt(this.dataset.height);
                const name = this.dataset.name;
                
                currentDevice = { width, height, name };
                
                // Update iframe and device info
                updateDeviceFrame(width, height, name);
            });
        });
        
        function updateDeviceFrame(width, height, name) {
            const frame = document.getElementById('testFrame');
            const deviceInfo = document.getElementById('deviceInfo');
            const deviceFrame = document.getElementById('deviceFrame');
            
            // Scale down large screens to fit
            let displayWidth = width;
            let displayHeight = height;
            let scale = 1;
            
            const maxWidth = window.innerWidth - 100;
            const maxHeight = window.innerHeight - 400;
            
            if (width > maxWidth || height > maxHeight) {
                const scaleX = maxWidth / width;
                const scaleY = maxHeight / height;
                scale = Math.min(scaleX, scaleY, 0.8);
                displayWidth = width * scale;
                displayHeight = height * scale;
            }
            
            frame.width = width;
            frame.height = height;
            deviceFrame.style.width = displayWidth + 'px';
            deviceFrame.style.height = displayHeight + 'px';
            deviceFrame.style.transform = `scale(${scale})`;
            deviceFrame.style.transformOrigin = 'center center';
            
            deviceInfo.textContent = `${name} - ${width}x${height}${scale < 1 ? ` (${Math.round(scale * 100)}%)` : ''}`;
            
            // Reset test results
            resetTestResults();
            logResult(`Switched to ${name} (${width}x${height})`);
        }
        
        function resetTestResults() {
            document.querySelectorAll('.test-checkbox').forEach(cb => cb.checked = false);
            document.querySelectorAll('.test-status').forEach(status => {
                status.className = 'test-status status-pending';
                status.textContent = 'Pending';
            });
        }
        
        function logResult(message) {
            const panel = document.getElementById('resultsPanel');
            const timestamp = new Date().toLocaleTimeString();
            panel.textContent += `[${timestamp}] ${message}\n`;
            panel.scrollTop = panel.scrollHeight;
        }
        
        function runResponsivenessTests() {
            logResult('Starting responsiveness tests...');
            
            const iframe = document.getElementById('testFrame');
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            try {
                // Test 1: Check for horizontal scrolling
                const hasHorizontalScroll = iframeDoc.documentElement.scrollWidth > currentDevice.width;
                updateTestStatus('scroll', !hasHorizontalScroll);
                logResult(`Horizontal scroll check: ${hasHorizontalScroll ? 'FAIL' : 'PASS'}`);
                
                // Test 2: Check touch targets (for mobile devices)
                if (currentDevice.width <= 768) {
                    checkTouchTargetsInFrame(iframeDoc);
                } else {
                    updateTestStatus('touch', true);
                    logResult('Touch targets: PASS (Desktop device)');
                }
                
                // Test 3: Check navigation visibility
                const nav = iframeDoc.querySelector('nav');
                if (nav) {
                    const navVisible = window.getComputedStyle(nav).display !== 'none';
                    updateTestStatus('navigation', navVisible);
                    logResult(`Navigation visibility: ${navVisible ? 'PASS' : 'FAIL'}`);
                }
                
                // Test 4: Check layout integrity
                const main = iframeDoc.querySelector('main');
                if (main) {
                    const mainRect = main.getBoundingClientRect();
                    const layoutOk = mainRect.width <= currentDevice.width;
                    updateTestStatus('layout', layoutOk);
                    logResult(`Layout integrity: ${layoutOk ? 'PASS' : 'FAIL'}`);
                }
                
                // Test 5: Check image scaling
                const images = iframeDoc.querySelectorAll('img');
                let imageScalingOk = true;
                images.forEach(img => {
                    if (img.offsetWidth > currentDevice.width) {
                        imageScalingOk = false;
                    }
                });
                updateTestStatus('images', imageScalingOk);
                logResult(`Image scaling: ${imageScalingOk ? 'PASS' : 'FAIL'}`);
                
                // Test 6: Check text readability (basic font size check)
                const textElements = iframeDoc.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6');
                let textReadable = true;
                textElements.forEach(el => {
                    const fontSize = parseFloat(window.getComputedStyle(el).fontSize);
                    if (fontSize < 12) { // Minimum readable font size
                        textReadable = false;
                    }
                });
                updateTestStatus('text', textReadable);
                logResult(`Text readability: ${textReadable ? 'PASS' : 'FAIL'}`);
                
                logResult('Responsiveness tests completed!');
                
            } catch (error) {
                logResult(`Error running tests: ${error.message}`);
            }
        }
        
        function checkTouchTargetsInFrame(doc) {
            const touchElements = doc.querySelectorAll('button, a, input[type="button"], input[type="submit"], [role="button"]');
            let touchTargetsOk = true;
            let smallTargets = 0;
            
            touchElements.forEach(el => {
                const rect = el.getBoundingClientRect();
                if (rect.width < 44 || rect.height < 44) {
                    touchTargetsOk = false;
                    smallTargets++;
                }
            });
            
            updateTestStatus('touch', touchTargetsOk);
            logResult(`Touch targets: ${touchTargetsOk ? 'PASS' : 'FAIL'} (${smallTargets} small targets found)`);
        }
        
        function updateTestStatus(testId, passed) {
            const checkbox = document.getElementById(`test-${testId}`);
            const status = document.getElementById(`status-${testId}`);
            
            checkbox.checked = passed;
            status.className = `test-status ${passed ? 'status-pass' : 'status-fail'}`;
            status.textContent = passed ? 'Pass' : 'Fail';
        }
        
        function checkTouchTargets() {
            logResult('Checking touch targets...');
            const iframe = document.getElementById('testFrame');
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            checkTouchTargetsInFrame(iframeDoc);
        }
        
        function checkHorizontalScroll() {
            logResult('Checking for horizontal scroll...');
            const iframe = document.getElementById('testFrame');
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            const hasHorizontalScroll = iframeDoc.documentElement.scrollWidth > currentDevice.width;
            const scrollWidth = iframeDoc.documentElement.scrollWidth;
            
            logResult(`Viewport width: ${currentDevice.width}px`);
            logResult(`Content width: ${scrollWidth}px`);
            logResult(`Horizontal scroll: ${hasHorizontalScroll ? 'YES (ISSUE)' : 'NO (GOOD)'}`);
        }
        
        function checkViewportInfo() {
            logResult('Viewport information:');
            logResult(`Device: ${currentDevice.name}`);
            logResult(`Dimensions: ${currentDevice.width}x${currentDevice.height}`);
            logResult(`Aspect ratio: ${(currentDevice.width / currentDevice.height).toFixed(2)}`);
            logResult(`Category: ${getDeviceCategory()}`);
        }
        
        function getDeviceCategory() {
            if (currentDevice.width >= 1200) return 'Desktop';
            if (currentDevice.width >= 768) return 'Tablet';
            return 'Mobile';
        }
        
        function takeScreenshot() {
            logResult('Screenshot functionality would require additional browser permissions.');
            logResult('Use browser dev tools (F12) to take screenshots manually.');
        }
        
        function clearResults() {
            document.getElementById('resultsPanel').textContent = 'Results cleared. Ready for new tests.\n';
            resetTestResults();
        }
        
        // Initialize with default device
        updateDeviceFrame(1920, 1080, 'Desktop FHD');
    </script>
</body>
</html>
